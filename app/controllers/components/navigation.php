<?php
class NavigationComponent extends Component {
    private $Destination;
    private $HolidayType;
    private $Spotlight;
    private $Page;
    private $NavigationMenu;
    private $db;
    private $initialized = false;

    public function initialize(&$controller) {
        if (!$this->initialized) {
            // Initialize models properly
            $this->Destination = ClassRegistry::init('Destination');
            $this->HolidayType = ClassRegistry::init('HolidayType');
            $this->Spotlight = ClassRegistry::init('Spotlight');
            $this->Page = ClassRegistry::init('Page');
            $this->NavigationMenu = ClassRegistry::init('NavigationMenu');
            $this->db = ConnectionManager::getDataSource('default');
            $this->initialized = true;
            file_put_contents(dirname(__FILE__).'/../../tmp/logs/navigation_component_loaded.txt', 'loaded:'.date('c').PHP_EOL, FILE_APPEND);
        }
    }

    private function ensureInitialized() {
        if (!$this->initialized) {
            $this->initialize(null);
        }
    }

    public function getNavigationData() {
        $this->ensureInitialized();

        // Fetch all navigation data
        $usaDestinations = $this->getUsaDestinations();
        $canadaDestinations = $this->getCanadaDestinations();
        $holidayTypes = $this->getHolidayTypes();
        $whatsHot = $this->getWhatsHot();
        $holidayInfoPages = $this->getHolidayInfoPages();
        $aboutPages = $this->getAboutPages();
        $farWideMenuItems = $this->getFarWideMenuItems();

        // Transform destinations to match expected structure
        $usaDestinations = array_map(function($dest) {
            $item = array(
                'Destination' => array(
                    'id' => $dest['Destination']['id'],
                    'name' => $dest['Destination']['name'],
                    'slug' => $dest['Destination']['slug']
                )
            );

            // Add children if they exist
            if (isset($dest['children']) && !empty($dest['children'])) {
                $item['Destination']['has_children'] = true;
                $item['Destination']['children'] = array_map(function($child) {
                    return array(
                        'Destination' => array(
                            'id' => $child['Destination']['id'],
                            'name' => $child['Destination']['name'],
                            'slug' => $child['Destination']['slug']
                        )
                    );
                }, $dest['children']);
            } else {
                $item['Destination']['has_children'] = false;
            }

            return $item;
        }, $usaDestinations ?: array());

        $canadaDestinations = array_map(function($dest) {
            $item = array(
                'Destination' => array(
                    'id' => $dest['Destination']['id'],
                    'name' => $dest['Destination']['name'],
                    'slug' => $dest['Destination']['slug']
                )
            );

            // Add children if they exist
            if (isset($dest['children']) && !empty($dest['children'])) {
                $item['Destination']['has_children'] = true;
                $item['Destination']['children'] = array_map(function($child) {
                    return array(
                        'Destination' => array(
                            'id' => $child['Destination']['id'],
                            'name' => $child['Destination']['name'],
                            'slug' => $child['Destination']['slug']
                        )
                    );
                }, $dest['children']);
            } else {
                $item['Destination']['has_children'] = false;
            }

            return $item;
        }, $canadaDestinations ?: array());

        // Transform holiday types for navigation
        $holidayTypesNav = array(
            'text' => 'Holiday Types',
            'url' => array('controller' => 'holiday_types', 'action' => 'index'),
            'has_children' => !empty($holidayTypes),
            'children' => array_map(function($type) {
                return array(
                    'text' => $type['HolidayType']['name'],
                    'url' => array(
                        'controller' => 'holiday_types',
                        'action' => 'view',
                        'holiday_type_slug' => $type['HolidayType']['slug']
                    )
                );
            }, $holidayTypes ?: array())
        );

        return array(
            'mainNav' => array(
                'usa' => array(
                    'text' => 'USA',
                    'url' => '/destinations/usa_holidays',
                    'items' => $usaDestinations
                ),
                'canada' => array(
                    'text' => 'Canada',
                    'url' => '/destinations/canada_holidays',
                    'items' => $canadaDestinations
                ),
                'holiday_types' => array(
                    'text' => 'Holiday Types',
                    'url' => '/holidays',
                    'items' => array($holidayTypesNav)
                )
            ),
            'usaDestinations' => $usaDestinations,
            'canadaDestinations' => $canadaDestinations,
            'holidayTypes' => $holidayTypes,
            'whatsHot' => $whatsHot,
            'holidayInfoPages' => $holidayInfoPages,
            'aboutPages' => $aboutPages,
            'farWideMenuItems' => $farWideMenuItems
        );
    }

    protected function getUsaDestinations() {
        $this->ensureInitialized();

        $allowedRefs = array(
            'USA A-Z by STATE',
            'USA A-Z by CITY',
            'California Holidays',
            'Florida Holidays',
            'The West',
            'Deep South Holidays',
            'Hawaii Holidays',
            'East Coast Holidays',
            'New England Holidays',
            'Pacific Northwest Holidays',
            'Rocky Mountains Holidays',
            'Texas Holidays',
            'Central States Holidays',
            'Great Lakes Holidays',
            'Capital Region Holidays',
            'The Carolinas Holidays',
            'Alaska Holidays',
            'Need Help Finding Somewhere?',
            'The Islands of The Bahamas'
        );

        // Get Route 66 separately to add to top level
        $route66 = $this->Destination->find('first', array(
            'conditions' => array(
                'Destination.slug' => 'route_66',
                'Destination.published' => 1
            ),
            'fields' => array('Destination.id', 'Destination.name', 'Destination.slug', 'Destination.direct_child_count'),
            'recursive' => -1
        ));

        $destinations = $this->Destination->find('all', array(
            'conditions' => array(
                'Destination.parent_id' => 1,
                'Destination.published' => 1,
                'Destination.name' => $allowedRefs
            ),
            'fields' => array('Destination.id', 'Destination.name', 'Destination.slug', 'Destination.direct_child_count'),
            'order' => sprintf("FIELD(Destination.name, %s)", implode(',', array_map(function($ref) {
                return "'" . str_replace("'", "\\'", $ref) . "'";
            }, $allowedRefs))),
            'recursive' => -1
        ));

        // Add Route 66 after the A-Z links if found
        if (!empty($route66)) {
            // Rename Route 66 for display
            $route66['Destination']['name'] = 'Route 66 Holidays';

            // Find the position after the A-Z links (USA A-Z by STATE and USA A-Z by CITY)
            $insertPosition = 0;
            foreach ($destinations as $index => $dest) {
                if (strpos($dest['Destination']['name'], 'USA A-Z') !== false) {
                    $insertPosition = $index + 1;
                }
            }

            // Insert Route 66 at the calculated position
            array_splice($destinations, $insertPosition, 0, array($route66));
        }

        // Get child destinations for each parent
        foreach ($destinations as &$destination) {
            if ($destination['Destination']['direct_child_count'] > 0) {
                $children = $this->Destination->find('all', array(
                    'conditions' => array(
                        'Destination.parent_id' => $destination['Destination']['id'],
                        'Destination.published' => 1
                    ),
                    'fields' => array('Destination.id', 'Destination.name', 'Destination.slug'),
                    'order' => array('Destination.name' => 'ASC'),
                    'recursive' => -1
                ));

                $destination['children'] = array_map(function($child) {
                    return array(
                        'Destination' => array(
                            'id' => $child['Destination']['id'],
                            'name' => $child['Destination']['name'],
                            'slug' => $child['Destination']['slug']
                        )
                    );
                }, $children);
            }
        }

        return $destinations;
    }

    protected function getCanadaDestinations() {
        $this->ensureInitialized();

        // First get British Columbia and other main destinations
        $destinations = $this->Destination->find('all', array(
            'conditions' => array(
                'Destination.parent_id' => 2, // Canada's ID
                'Destination.published' => 1,
                'NOT' => array(
                    'Destination.name LIKE' => '%A-Z by%'
                )
            ),
            'fields' => array(
                'Destination.id',
                'Destination.name',
                'Destination.slug',
                'Destination.direct_child_count'
            ),
            'order' => array('Destination.name' => 'ASC'),
            'recursive' => -1
        ));

        // Get child destinations for each parent
        foreach ($destinations as &$destination) {
            if ($destination['Destination']['direct_child_count'] > 0) {
                $children = $this->Destination->find('all', array(
                    'conditions' => array(
                        'Destination.parent_id' => $destination['Destination']['id'],
                        'Destination.published' => 1
                    ),
                    'fields' => array(
                        'Destination.id',
                        'Destination.name',
                        'Destination.slug'
                    ),
                    'order' => array('Destination.name' => 'ASC'),
                    'recursive' => -1
                ));

                if (!empty($children)) {
                    $destination['children'] = array_map(function($child) {
                return array(
                            'Destination' => array(
                                'id' => $child['Destination']['id'],
                                'name' => $child['Destination']['name'],
                                'slug' => $child['Destination']['slug']
                            )
                        );
                    }, $children);
                }
            }
        }

        return $destinations;
    }

    protected function getHolidayTypes() {
        $this->ensureInitialized();
        return $this->HolidayType->find('all', array(
            'conditions' => array(
                'HolidayType.published' => 1
            ),
            'fields' => array('HolidayType.id', 'HolidayType.name', 'HolidayType.slug'),
            'order' => array('HolidayType.order' => 'ASC'),
            'recursive' => -1
        ));
    }

    protected function getWhatsHot() {
        $this->ensureInitialized();
        return $this->Spotlight->find('all', array(
            'conditions' => array(
                'Spotlight.published' => 1,
                'Spotlight.expiry_date >=' => date('Y-m-d')
            ),
            'fields' => array('Spotlight.id', 'Spotlight.name', 'Spotlight.slug'),
            'order' => array('Spotlight.order' => 'ASC'),
            'recursive' => -1
        ));
    }

    protected function getHolidayInfoPages() {
        $this->ensureInitialized();
        Cache::delete('main_navigation', 'navigation');

        // Get ALL holiday info pages
        $pages = $this->Page->find('all', array(
            'conditions' => array(
                'Page.css_class' => 'holiday_information'
            ),
            'fields' => array('id', 'internal_ref', 'slug', 'parent_id', 'published'),
            'order' => array('Page.internal_ref ASC'),
            'recursive' => -1
        ));

        // Debug the raw pages data
        // error_log("\nRaw pages from database (id, internal_ref, parent_id, published):");
        // foreach ($pages as $page) {
        //     $pageInfo = $page['Page'];
        //     error_log(sprintf("Page: %d, %s, parent: %s, published: %d",
        //         $pageInfo['id'],
        //         $pageInfo['internal_ref'],
        //         $pageInfo['parent_id'] ?: 'none',
        //         $pageInfo['published']
        //     ));
        // }

        // Create maps for pages and their children
        $pageMap = array();
        $childrenMap = array();
        foreach ($pages as $page) {
            $pageId = $page['Page']['id'];
            $pageMap[$pageId] = $page;

            $parentId = $page['Page']['parent_id'];
            if ($parentId) {
                if (!isset($childrenMap[$parentId])) {
                    $childrenMap[$parentId] = array();
                }
                $childrenMap[$parentId][] = $page;
                // error_log(sprintf("Added page %d (%s) as child of %d",
                //     $pageId,
                //     $page['Page']['internal_ref'],
                //     $parentId
                // ));
            }
        }

        // Debug output for children map
        // error_log("\nFull Children Map:");
        // foreach ($childrenMap as $parentId => $children) {
        //     error_log(sprintf("Parent %d has children: %s",
        //         $parentId,
        //         implode(', ', array_map(function($child) {
        //             return sprintf("%d (%s)", $child['Page']['id'], $child['Page']['internal_ref']);
        //         }, $children))
        //     ));
        // }

        // Define the order of top-level items
        $orderedTopLevelIds = array(23, 14, 7, 46, 21, 22);

        // Helper function to build children recursively
        $buildChildren = function($parentId) use (&$buildChildren, $childrenMap, $pageMap) {
            if (!isset($childrenMap[$parentId])) {
                return array();
            }

            $children = array();
            foreach ($childrenMap[$parentId] as $child) {
                $childId = $child['Page']['id'];
                $isPublished = $child['Page']['published'];
                $hasChildren = isset($childrenMap[$childId]);

                // Skip unpublished pages that don't have children
                if (!$isPublished && !$hasChildren) {
                    // error_log(sprintf("Skipping unpublished page %d (%s) with no children",
                    //     $childId,
                    //     $child['Page']['internal_ref']
                    // ));
                    continue;
                }

                // Start with the basic child data
                $childData = $child;

                // For unpublished pages, remove the slug so no link is generated
                if (!$isPublished) {
                    unset($childData['Page']['slug']);
                    // error_log(sprintf("Removed slug from unpublished page %d (%s)",
                    //     $childId,
                    //     $child['Page']['internal_ref']
                    // ));
                }

                // Check if this child has its own children
                if ($hasChildren) {
                    // Recursively get all descendants
                    $childChildren = $buildChildren($childId);
                    if (!empty($childChildren)) {
                        $childData['Page']['children'] = $childChildren;
                        // error_log(sprintf("Added %d children to %d (%s)",
                        //     count($childChildren),
                        //     $childId,
                        //     $child['Page']['internal_ref']
                        // ));
                    } else if (!$isPublished) {
                        // If unpublished and no valid children, skip this page
                        // error_log(sprintf("Skipping unpublished page %d (%s) as it has no valid children",
                        //     $childId,
                        //     $child['Page']['internal_ref']
                        // ));
                        continue;
                    }
                }

                $children[] = $childData;
            }

            return $children;
        };

        // Build the final array of pages with their children
        $result = array();
        foreach ($orderedTopLevelIds as $id) {
            if (isset($pageMap[$id])) {
                $page = $pageMap[$id];
                $isPublished = $page['Page']['published'];

                // error_log(sprintf("\nProcessing top-level page %d (%s) - published: %d",
                //     $id,
                //     $page['Page']['internal_ref'],
                //     $isPublished
                // ));

                // For unpublished pages, remove the slug so no link is generated
                if (!$isPublished) {
                    unset($page['Page']['slug']);
                    // error_log(sprintf("Removed slug from unpublished top-level page %d (%s)",
                    //     $id,
                    //     $page['Page']['internal_ref']
                    // ));
                }

                // Build complete hierarchy for this page
                if (isset($childrenMap[$id])) {
                    // error_log(sprintf("Building hierarchy for %d (%s)",
                    //     $id,
                    //     $page['Page']['internal_ref']
                    // ));
                    $children = $buildChildren($id);
                    if (!empty($children)) {
                        $page['Page']['children'] = $children;
                        // error_log(sprintf("Added %d children to top-level page %d (%s)",
                        //     count($children),
                        //     $id,
                        //     $page['Page']['internal_ref']
                        // ));
                    } else if (!$isPublished) {
                        // Skip unpublished top-level pages that have no valid children
                        // error_log(sprintf("Skipping unpublished top-level page %d (%s) as it has no valid children",
                        //     $id,
                        //     $page['Page']['internal_ref']
                        // ));
                        continue;
                    }
                } else if (!$isPublished) {
                    // Skip unpublished top-level pages with no children
                    // error_log(sprintf("Skipping unpublished top-level page %d (%s) with no children",
                    //     $id,
                    //     $page['Page']['internal_ref']
                    // ));
                    continue;
                }

                $result[] = $page;
            }
        }

        return $result;
    }

    protected function getAboutPages() {
        $this->ensureInitialized();
        Cache::delete('main_navigation', 'navigation');

        $allowedRefs = array(
            'About Us',
            'Our Customers Say',
            'Fully Bonded for Your Protection',
            'Telephone Numbers',
            'Address and Registered Details',
            'Bon Voyage Feefo Rating',
            'Finding Us',
            'Careers',
            'Press Centre'
        );

        $pages = $this->Page->find('all', array(
            'conditions' => array(
                'Page.published' => 1,
                'Page.css_class' => 'about_bon_voyage',
                'Page.internal_ref' => $allowedRefs
            ),
            'fields' => array('Page.id', 'Page.internal_ref', 'Page.slug'),
            'order' => sprintf("FIELD(Page.internal_ref, %s)", implode(',', array_map(function($ref) {
                return "'" . str_replace("'", "\\'", $ref) . "'";
            }, $allowedRefs))),
            'recursive' => -1
        ));

        // Transform pages into navigation format
        return array_map(function($page) {
            return array(
                'Page' => array(
                    'id' => $page['Page']['id'],
                    'internal_ref' => $page['Page']['internal_ref'],
                    'slug' => $page['Page']['slug']
                )
            );
        }, $pages ?: array());
    }

    /**
     * Get Far & Wide menu items from the database
     * Falls back to hardcoded values if no database items exist
     *
     * @return array Far & Wide menu items
     */
    protected function getFarWideMenuItems() {
        $this->ensureInitialized();

        // Try to get menu items from database first
        $cacheKey = 'navigation_menu_far_wide';
        $menuItems = Cache::read($cacheKey, 'navigation');

        if ($menuItems === false) {
            $menuItems = $this->NavigationMenu->getMenuItems('far_wide', true);
            Cache::write($cacheKey, $menuItems, 'navigation');
        }

        // If no database items exist, fall back to hardcoded values for backward compatibility
        if (empty($menuItems)) {
            $menuItems = array(
                array(
                    'text' => 'South America',
                    'url' => '/landing_pages/south_america'
                ),
                array(
                    'text' => 'Worldwide',
                    'url' => '/destinations/worldwide_holidays'
                ),
                array(
                    'text' => 'Cruises',
                    'url' => '/landing_pages/cruises'
                )
            );
        }

        return $menuItems;
    }
}
