<?php
/**
 * Check the current state of the navigation_menus table
 * Run this script to see what's currently in the table
 */

// Include CakePHP bootstrap
require_once dirname(__FILE__) . '/../../webroot/index.php';

// Get database connection
$db = ConnectionManager::getDataSource('default');

echo "Checking navigation_menus table...\n\n";

try {
    // Check if table exists
    $result = $db->query("SHOW TABLES LIKE 'navigation_menus'");
    if (empty($result)) {
        echo "❌ navigation_menus table does not exist.\n";
        exit(1);
    }
    
    echo "✅ navigation_menus table exists.\n\n";
    
    // Check current data
    $data = $db->query("SELECT * FROM navigation_menus ORDER BY menu_type, lft");
    
    if (empty($data)) {
        echo "📋 Table is empty - no menu items found.\n";
    } else {
        echo "📋 Current menu items:\n";
        echo str_repeat("-", 80) . "\n";
        printf("%-4s %-10s %-3s %-4s %-20s %-30s %-10s\n", 
               "ID", "Parent", "Lft", "Rght", "Name", "URL", "Menu Type");
        echo str_repeat("-", 80) . "\n";
        
        foreach ($data as $row) {
            $item = $row['navigation_menus'];
            printf("%-4s %-10s %-3s %-4s %-20s %-30s %-10s\n",
                   $item['id'],
                   $item['parent_id'] ?: 'NULL',
                   $item['lft'],
                   $item['rght'],
                   substr($item['name'], 0, 19),
                   substr($item['url'], 0, 29),
                   $item['menu_type']
            );
        }
        echo str_repeat("-", 80) . "\n";
        echo "Total items: " . count($data) . "\n\n";
        
        // Show Far & Wide items specifically
        $farWideData = $db->query("SELECT * FROM navigation_menus WHERE menu_type = 'far_wide' ORDER BY lft");
        if (!empty($farWideData)) {
            echo "🌍 Far & Wide menu items:\n";
            foreach ($farWideData as $row) {
                $item = $row['navigation_menus'];
                $indent = str_repeat("  ", $item['parent_id'] ? 1 : 0);
                echo $indent . "- " . $item['name'] . " (" . $item['url'] . ")\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
