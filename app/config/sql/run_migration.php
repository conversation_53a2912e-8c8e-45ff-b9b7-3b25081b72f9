<?php
/**
 * Simple migration runner for adding navigation_menus table
 * Run this script from the command line: php run_migration.php
 */

// Include CakePHP bootstrap
require_once dirname(__FILE__) . '/../../webroot/index.php';

// Get database connection
$db = ConnectionManager::getDataSource('default');

echo "Running navigation_menus data migration...\n";

// Check if table exists
$tableExists = $db->execute("SHOW TABLES LIKE 'navigation_menus'");
if (!$tableExists) {
    echo "Error: navigation_menus table does not exist. Please ensure the table is created first.\n";
    exit(1);
}

try {
    // Read and execute the migration SQL
    $migrationSql = file_get_contents(dirname(__FILE__) . '/migrations/add_navigation_menus_table.sql');

    // Split by semicolon and execute each statement
    $statements = explode(';', $migrationSql);

    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            echo "Executing: " . substr($statement, 0, 50) . "...\n";
            $result = $db->execute($statement);
            if (!$result) {
                throw new Exception("Failed to execute statement: " . substr($statement, 0, 100));
            }
        }
    }

    echo "Migration completed successfully!\n";
    echo "The navigation_menus table has been populated with Far & Wide menu data.\n";
    echo "You can now manage Far & Wide navigation items in the CMS at /webadmin/navigation_menus\n";

} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
