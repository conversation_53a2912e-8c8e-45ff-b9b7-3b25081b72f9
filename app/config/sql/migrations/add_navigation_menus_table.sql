-- Migration: Add navigation_menus table for configurable navigation
-- Date: 2025-07-15

-- Create navigation_menus table
DROP TABLE IF EXISTS `navigation_menus`;
CREATE TABLE `navigation_menus` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `parent_id` int(10) unsigned default NULL,
  `lft` int(10) unsigned default NULL,
  `rght` int(10) unsigned default NULL,
  `child_count` int(10) unsigned default NULL,
  `direct_child_count` int(10) unsigned default NULL,
  `name` varchar(255) NOT NULL default '',
  `url` varchar(500) NOT NULL default '',
  `menu_type` varchar(50) NOT NULL default 'main_nav',
  `order` int(10) unsigned default NULL,
  `published` tinyint(1) unsigned NOT NULL default '1',
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`),
  KEY `parent_id` (`parent_id`),
  KEY `menu_type` (`menu_type`),
  KEY `published` (`published`),
  KEY `lft` (`lft`),
  KEY `rght` (`rght`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- Insert seed data for Far & Wide menu (maintaining current functionality)
INSERT INTO `navigation_menus` (`id`, `parent_id`, `lft`, `rght`, `child_count`, `direct_child_count`, `name`, `url`, `menu_type`, `order`, `published`, `created`, `modified`) VALUES
(1, NULL, 1, 8, 3, 3, 'Far & Wide', '#', 'far_wide', 1, 1, NOW(), NOW()),
(2, 1, 2, 3, 0, 0, 'South America', '/landing_pages/south_america', 'far_wide', 1, 1, NOW(), NOW()),
(3, 1, 4, 5, 0, 0, 'Worldwide', '/destinations/worldwide_holidays', 'far_wide', 2, 1, NOW(), NOW()),
(4, 1, 6, 7, 0, 0, 'Cruises', '/landing_pages/cruises', 'far_wide', 3, 1, NOW(), NOW());
