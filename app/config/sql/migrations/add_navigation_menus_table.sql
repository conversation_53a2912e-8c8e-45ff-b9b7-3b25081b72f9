-- Migration: Populate navigation_menus table for Far & Wide navigation
-- Date: 2025-07-15
-- Note: Table already exists in schema, this just populates it with Far & Wide data

-- Clear existing Far & Wide menu items
DELETE FROM `navigation_menus` WHERE `menu_type` = 'far_wide';

-- Insert seed data for Far & Wide menu with hierarchical structure
-- All URLs use /landing_pages/ prefix, Cruises and Escorted Tours are children of Worldwide
INSERT INTO `navigation_menus` (`id`, `parent_id`, `lft`, `rght`, `child_count`, `direct_child_count`, `name`, `url`, `menu_type`, `order`, `published`, `created`, `modified`) VALUES
(1, NULL, 1, 10, 4, 2, 'Far & Wide', '#', 'far_wide', 1, 1, NOW(), NOW()),
(2, 1, 2, 3, 0, 0, 'South America', '/landing_pages/south_america', 'far_wide', 1, 1, NOW(), NOW()),
(3, 1, 4, 9, 2, 2, 'Worldwide', '/landing_pages/worldwide', 'far_wide', 2, 1, NOW(), NOW()),
(4, 3, 5, 6, 0, 0, 'Cruises', '/landing_pages/cruises', 'far_wide', 1, 1, NOW(), NOW()),
(5, 3, 7, 8, 0, 0, 'Escorted Tours', '/landing_pages/escorted_tours', 'far_wide', 2, 1, NOW(), NOW());
