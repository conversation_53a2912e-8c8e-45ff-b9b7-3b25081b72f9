<?php
// Helper function to render destination items
    function renderDestinationItems($destinations, $title = 'Popular Destinations') {
        if (empty($destinations)) return '';

        $html = '<div class="mega-menu__section">';
        $html .= '<h3>' . h($title) . '</h3>';
        $html .= '<ul class="mega-menu__list mega-menu__list--columns">';

        foreach ($destinations as $dest) {
            $hasChildren = !empty($dest['Destination']['children']);
            $html .= '<li class="mega-menu__item' . ($hasChildren ? ' has-children' : '') . '">';
            $html .= '<a href="/destinations/' . h($dest['Destination']['slug']) . '" class="mega-menu__link" title="' . h($dest['Destination']['name']) . '">';
            $html .= h($dest['Destination']['name']);
            $html .= '</a>';

            if ($hasChildren) {
                $html .= '<ul class="mega-menu__sublist">';
                foreach ($dest['Destination']['children'] as $child) {
                    $html .= '<li class="mega-menu__subitem">';
                    $html .= '<a href="/destinations/' . h($child['Destination']['slug']) . '" class="mega-menu__sublink" title="' . h($child['Destination']['name']) . '">';
                    $html .= h($child['Destination']['name']);
                    $html .= '</a>';
                    $html .= '</li>';
                }
                $html .= '</ul>';
            }

            $html .= '</li>';
        }

        $html .= '</ul></div>';
        return $html;
    }

    // Helper function to render simple list items
    function renderSimpleItems($items, $type, $title) {
        if (empty($items)) return '';

        $html = '<div class="mega-menu__section">';
        $html .= '<h3>' . h($title) . '</h3>';
        $html .= '<ul class="mega-menu__list mega-menu__list--columns">';

        foreach ($items as $item) {
            $html .= '<li class="mega-menu__item">';
            $html .= '<a href="/' . $type . '/' . h($item[ucfirst($type)]['slug']) . '" class="mega-menu__link" title="' . h($item[ucfirst($type)]['name'] ?? $item[ucfirst($type)]['internal_ref']) . '">';
            $html .= h($item[ucfirst($type)]['name'] ?? $item[ucfirst($type)]['internal_ref']);
            $html .= '</a></li>';
        }

        $html .= '</ul></div>';
        return $html;
    }

    // Helper function to render page items
    function renderPageItems($pages, $title) {
        if (empty($pages)) return '';

        $html = '<div class="mega-menu__section">';
        $html .= '<h3>' . h($title) . '</h3>';
        $html .= '<ul class="mega-menu__list mega-menu__list--columns">';

        foreach ($pages as $page) {
            $html .= '<li class="mega-menu__item">';
            $html .= '<a href="/page/' . h($page['Page']['slug']) . '" class="mega-menu__link" title="' . h($page['Page']['internal_ref']) . '">';
            $html .= h($page['Page']['internal_ref']);
            $html .= '</a></li>';
        }

        $html .= '</ul></div>';
        return $html;
    }
    ?>

    <!-- USA Dropdown -->
    <div class="mega-menu__panel" id="usa-dropdown">
        <div class="mega-menu__inner">
            <?php
            if (!empty($usaDestinations)) {
                echo renderDestinationItems($usaDestinations);
            }
            ?>
        </div>
    </div>

    <!-- Canada Dropdown -->
    <div class="mega-menu__panel" id="canada-dropdown">
        <div class="mega-menu__inner">
            <?php
            if (!empty($canadaDestinations)) {
                echo renderDestinationItems($canadaDestinations);
            }
            ?>
        </div>
    </div>

    <!-- Far & Wide Dropdown -->
    <div class="mega-menu__panel" id="far-wide-dropdown">
        <div class="mega-menu__inner">
            <div class="mega-menu__section">
                <h3>Far & Wide</h3>
                <ul class="mega-menu__list mega-menu__list--columns">
                    <?php if (!empty($farWideMenuItems)): ?>
                        <?php foreach ($farWideMenuItems as $item): ?>
                            <li class="mega-menu__item">
                                <a href="<?php echo h($item['url']); ?>" class="mega-menu__link" title="<?php echo h($item['text']); ?>">
                                    <?php echo h($item['text']); ?>
                                </a>
                                <?php if (!empty($item['children'])): ?>
                                    <ul class="mega-menu__sublist">
                                        <?php foreach ($item['children'] as $child): ?>
                                            <li class="mega-menu__subitem">
                                                <a href="<?php echo h($child['url']); ?>" class="mega-menu__sublink" title="<?php echo h($child['text']); ?>">
                                                    <?php echo h($child['text']); ?>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php endif; ?>
                            </li>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- Holiday Types Dropdown -->
    <div class="mega-menu__panel" id="holiday-types-dropdown">
        <div class="mega-menu__inner">
            <?php if (!empty($holidayTypes)): ?>
                <div class="mega-menu__section">
                    <h3>Holiday Types</h3>
                    <ul class="mega-menu__list mega-menu__list--columns">
                        <?php foreach ($holidayTypes as $type): ?>
                            <li class="mega-menu__item">
                                <?php
                                // Handle both formats: HolidayType format and text/url format
                                if (isset($type['HolidayType'])) {
                                    $name = $type['HolidayType']['name'];
                                    $url = '/holidays/' . $type['HolidayType']['slug'];
                                } else {
                                    $name = $type['text'];
                                    $url = $type['url'];
                                }
                                ?>
                                <a href="<?php echo h($url); ?>" class="mega-menu__link" title="<?php echo h($name); ?>">
                                    <?php echo h($name); ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- What's Hot Dropdown -->
    <div class="mega-menu__panel" id="whats-hot-dropdown">
        <div class="mega-menu__inner">
            <?php
            if (!empty($whatsHot)) {
                echo renderSimpleItems($whatsHot, 'spotlight', "What's Hot");
            }
            ?>
        </div>
    </div>

    <!-- Holiday Information Dropdown -->
    <div class="mega-menu__panel" id="holiday-info-dropdown">
        <div class="mega-menu__inner">
            <?php
            if (!empty($holidayInfoPages)) {
                echo renderPageItems($holidayInfoPages, 'Holiday Information');
            }
            ?>
        </div>
    </div>

    <!-- About Us Dropdown -->
    <div class="mega-menu__panel" id="about-dropdown">
        <div class="mega-menu__inner">
            <?php
            if (!empty($aboutPages)) {
                echo renderPageItems($aboutPages, 'About Bon Voyage');
            }
            ?>
        </div>
    </div>
