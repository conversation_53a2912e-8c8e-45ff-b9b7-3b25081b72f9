-- MySQL dump 10.11
--
-- Host: localhost    Database: bonvoyage
-- ------------------------------------------------------
-- Server version	5.0.77-community-nt

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `accommodation`
--

DROP TABLE IF EXISTS `accommodation`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `accommodation` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) default NULL,
  `location` varchar(255) default NULL,
  `image_id` int(10) unsigned default NULL,
  `official_rating` varchar(255) default NULL,
  `b_v_rating` varchar(255) default NULL,
  `from_price` decimal(8,2) default NULL,
  `resort_fee_applicable` varchar(255) default NULL,
  `minimum_stay` varchar(255) default NULL,
  `b_v_added_value` text,
  `our_recommendation` text,
  `preferred_status` tinyint(1) NOT NULL default '0',
  `highlights` text,
  `accommodation_type` varchar(255) default NULL,
  `latitude` double(11,6) default NULL,
  `longitude` double(11,6) default NULL,
  `meta_title` varchar(255) default NULL,
  `meta_description` text,
  `meta_keywords` text,
  `summary` text,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  `map_latitude` float default NULL,
  `map_longitude` float default NULL,
  `zoom_level` int(2) default NULL,
  `published` tinyint(1) NOT NULL default '0',
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=671 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `accommodation_accommodation_characteristics`
--

DROP TABLE IF EXISTS `accommodation_accommodation_characteristics`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `accommodation_accommodation_characteristics` (
  `accommodation_id` int(10) unsigned NOT NULL,
  `accommodation_characteristic_id` int(10) unsigned NOT NULL,
  UNIQUE KEY `accommodation_id` (`accommodation_id`,`accommodation_characteristic_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `accommodation_accommodation_facilities`
--

DROP TABLE IF EXISTS `accommodation_accommodation_facilities`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `accommodation_accommodation_facilities` (
  `accommodation_id` int(10) unsigned NOT NULL,
  `accommodation_facility_id` int(10) unsigned NOT NULL,
  UNIQUE KEY `accommodation_id` (`accommodation_id`,`accommodation_facility_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `accommodation_characteristics`
--

DROP TABLE IF EXISTS `accommodation_characteristics`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `accommodation_characteristics` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `name` varchar(255) NOT NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `accommodation_destinations`
--

DROP TABLE IF EXISTS `accommodation_destinations`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `accommodation_destinations` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `accommodation_id` int(10) unsigned NOT NULL,
  `destination_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `destination_id` (`destination_id`),
  KEY `accommodation_id` (`accommodation_id`)
) ENGINE=MyISAM AUTO_INCREMENT=3087 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `accommodation_facilities`
--

DROP TABLE IF EXISTS `accommodation_facilities`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `accommodation_facilities` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `name` varchar(255) NOT NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=36 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `accommodation_holiday_types`
--

DROP TABLE IF EXISTS `accommodation_holiday_types`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `accommodation_holiday_types` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `accommodation_id` int(10) unsigned NOT NULL,
  `holiday_type_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `destination_id` (`holiday_type_id`),
  KEY `accommodation_id` (`accommodation_id`)
) ENGINE=MyISAM AUTO_INCREMENT=741 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `accommodation_images`
--

DROP TABLE IF EXISTS `accommodation_images`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `accommodation_images` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `accommodation_id` int(10) unsigned NOT NULL,
  `image_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  PRIMARY KEY  (`id`),
  KEY `image_id` (`image_id`),
  KEY `destination_id` (`accommodation_id`)
) ENGINE=MyISAM AUTO_INCREMENT=1306 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `acos`
--

DROP TABLE IF EXISTS `acos`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `acos` (
  `id` int(10) NOT NULL auto_increment,
  `parent_id` int(10) default NULL,
  `model` varchar(255) default NULL,
  `foreign_key` int(10) default NULL,
  `alias` varchar(255) default NULL,
  `lft` int(10) default NULL,
  `rght` int(10) default NULL,
  PRIMARY KEY  (`id`),
  KEY `alias` (`alias`),
  KEY `parent_id` (`parent_id`),
  KEY `acos_idx1` (`lft`,`rght`),
  KEY `foreign_key` (`foreign_key`,`lft`)
) ENGINE=MyISAM AUTO_INCREMENT=355 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `activities`
--

DROP TABLE IF EXISTS `activities`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `activities` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) default NULL,
  `image_id` int(10) unsigned default NULL,
  `meta_title` varchar(255) default NULL,
  `meta_description` text,
  `meta_keywords` text,
  `summary` text,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  `published` tinyint(1) NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `image_id` (`image_id`)
) ENGINE=MyISAM AUTO_INCREMENT=719 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `activities_destinations`
--

DROP TABLE IF EXISTS `activities_destinations`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `activities_destinations` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `activity_id` int(10) unsigned NOT NULL,
  `destination_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `activity_id` (`activity_id`),
  KEY `destination_id` (`destination_id`)
) ENGINE=MyISAM AUTO_INCREMENT=3791 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `activities_holiday_types`
--

DROP TABLE IF EXISTS `activities_holiday_types`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `activities_holiday_types` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `activity_id` int(10) unsigned NOT NULL,
  `holiday_type_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `activity_id` (`activity_id`),
  KEY `destination_id` (`holiday_type_id`)
) ENGINE=MyISAM AUTO_INCREMENT=752 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `aros`
--

DROP TABLE IF EXISTS `aros`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `aros` (
  `id` int(10) NOT NULL auto_increment,
  `parent_id` int(10) default NULL,
  `model` varchar(255) default NULL,
  `foreign_key` int(10) default NULL,
  `alias` varchar(255) default NULL,
  `lft` int(10) default NULL,
  `rght` int(10) default NULL,
  PRIMARY KEY  (`id`),
  KEY `aro` (`model`,`foreign_key`)
) ENGINE=MyISAM AUTO_INCREMENT=29 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `aros_acos`
--

DROP TABLE IF EXISTS `aros_acos`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `aros_acos` (
  `id` int(10) NOT NULL auto_increment,
  `aro_id` int(10) NOT NULL,
  `aco_id` int(10) NOT NULL,
  `_create` varchar(2) NOT NULL default '0',
  `_read` varchar(2) NOT NULL default '0',
  `_update` varchar(2) NOT NULL default '0',
  `_delete` varchar(2) NOT NULL default '0',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `ARO_ACO_KEY` (`aro_id`,`aco_id`)
) ENGINE=MyISAM AUTO_INCREMENT=49 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `contacts`
--

DROP TABLE IF EXISTS `contacts`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `contacts` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `type` varchar(255) default NULL,
  `title` varchar(255) default NULL,
  `firstname` varchar(255) default NULL,
  `lastname` varchar(255) default NULL,
  `email` varchar(255) default NULL,
  `telephone` varchar(255) default NULL,
  `preferred_day` varchar(255) default NULL,
  `preferred_time` varchar(255) default NULL,
  `optin` tinyint(1) unsigned NOT NULL default '0',
  `question` text,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=3294 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `content_blocks`
--

DROP TABLE IF EXISTS `content_blocks`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `content_blocks` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `image_id` int(10) unsigned default NULL,
  `alignment` varchar(255) default NULL,
  `content` text,
  `model` varchar(255) NOT NULL,
  `modelid` int(10) unsigned NOT NULL,
  `link_text` varchar(255) default NULL,
  `link` varchar(255) default NULL,
  `order` int(10) unsigned NOT NULL,
  `youtube_video_id` varchar(255) default NULL,
  PRIMARY KEY  (`id`),
  KEY `image_id` (`image_id`),
  KEY `model_id` (`modelid`)
) ENGINE=MyISAM AUTO_INCREMENT=4986 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `custom_image_versions`
--

DROP TABLE IF EXISTS `custom_image_versions`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `custom_image_versions` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `image_id` int(10) unsigned NOT NULL,
  `image_version_id` int(10) unsigned NOT NULL,
  `source_left_offset` int(10) unsigned NOT NULL,
  `source_top_offset` int(10) unsigned NOT NULL,
  `source_width` int(10) unsigned NOT NULL,
  `source_height` int(10) unsigned NOT NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `destinations`
--

DROP TABLE IF EXISTS `destinations`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `destinations` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `parent_id` int(10) unsigned default NULL,
  `lft` int(10) unsigned default NULL,
  `rght` int(10) unsigned default NULL,
  `child_count` int(10) unsigned NOT NULL default '0',
  `direct_child_count` int(10) unsigned NOT NULL default '0',
  `latitude` float default NULL,
  `longitude` float default NULL,
  `zoom_level` int(2) default NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) default NULL,
  `summary` text,
  `main_image_id` int(10) unsigned default NULL,
  `meta_title` varchar(255) default NULL,
  `meta_description` text,
  `meta_keywords` text,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  `map_latitude` float default NULL,
  `map_longitude` float default NULL,
  `published` tinyint(1) NOT NULL default '0',
  `google_tracking` text,
  `youtube_playlist_id` varchar(16) default NULL,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=MyISAM AUTO_INCREMENT=589 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `destinations_images`
--

DROP TABLE IF EXISTS `destinations_images`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `destinations_images` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `destination_id` int(10) unsigned NOT NULL,
  `image_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `destination_id` (`destination_id`),
  KEY `image_id` (`image_id`)
) ENGINE=MyISAM AUTO_INCREMENT=3356 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `destinations_itineraries`
--

DROP TABLE IF EXISTS `destinations_itineraries`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `destinations_itineraries` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `destination_id` int(10) unsigned NOT NULL,
  `itinerary_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `destination_id` (`destination_id`),
  KEY `itinerary_id` (`itinerary_id`)
) ENGINE=MyISAM AUTO_INCREMENT=5625 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `destinations_landing_pages`
--

DROP TABLE IF EXISTS `destinations_landing_pages`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `destinations_landing_pages` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `destination_id` int(10) unsigned NOT NULL,
  `landing_page_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `UNIQUE` (`destination_id`,`landing_page_id`),
  KEY `destination_id` (`destination_id`)
) ENGINE=MyISAM AUTO_INCREMENT=24 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `destinations_on_holiday_types`
--

DROP TABLE IF EXISTS `destinations_on_holiday_types`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `destinations_on_holiday_types` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `destination_id` int(10) unsigned NOT NULL,
  `holiday_type_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `UNIQUE` (`destination_id`,`holiday_type_id`),
  KEY `destination_id` (`destination_id`)
) ENGINE=MyISAM AUTO_INCREMENT=222 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `download_folders`
--

DROP TABLE IF EXISTS `download_folders`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `download_folders` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `parent_id` int(10) unsigned default NULL,
  `lft` int(10) unsigned default NULL,
  `rght` int(10) unsigned default NULL,
  `child_count` int(10) unsigned NOT NULL default '0',
  `direct_child_count` int(10) unsigned NOT NULL default '0',
  `download_count` int(11) default NULL,
  `name` varchar(255) NOT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `downloads`
--

DROP TABLE IF EXISTS `downloads`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `downloads` (
  `id` int(11) NOT NULL auto_increment,
  `download_folder_id` int(11) default NULL,
  `extension` varchar(4) default NULL,
  `name` varchar(255) default NULL,
  `size` int(11) default NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `faqs`
--

DROP TABLE IF EXISTS `faqs`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `faqs` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `question` varchar(255) NOT NULL,
  `answer` text NOT NULL,
  `order` int(10) unsigned default NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=29 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `groups`
--

DROP TABLE IF EXISTS `groups`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `groups` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `name` varchar(255) NOT NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `holiday_types`
--

DROP TABLE IF EXISTS `holiday_types`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `holiday_types` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) default NULL,
  `image_id` int(10) unsigned default NULL,
  `meta_title` varchar(255) default NULL,
  `meta_description` text,
  `meta_keywords` text,
  `summary` text,
  `order` int(10) unsigned default NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  `published` tinyint(1) NOT NULL default '0',
  `google_tracking` text,
  `banner` int(10) unsigned default NULL,
  PRIMARY KEY  (`id`),
  KEY `image_id` (`image_id`)
) ENGINE=MyISAM AUTO_INCREMENT=23 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `holiday_types_itineraries`
--

DROP TABLE IF EXISTS `holiday_types_itineraries`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `holiday_types_itineraries` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `holiday_type_id` int(10) unsigned NOT NULL,
  `itinerary_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `destination_id` (`holiday_type_id`),
  KEY `itinerary_id` (`itinerary_id`)
) ENGINE=MyISAM AUTO_INCREMENT=1497 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `holiday_types_landing_pages`
--

DROP TABLE IF EXISTS `holiday_types_landing_pages`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `holiday_types_landing_pages` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `holiday_type_id` int(10) unsigned NOT NULL,
  `landing_page_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `UNIQUE` (`landing_page_id`,`holiday_type_id`),
  KEY `landing_page_id` (`landing_page_id`)
) ENGINE=MyISAM AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `holiday_types_on_destinations`
--

DROP TABLE IF EXISTS `holiday_types_on_destinations`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `holiday_types_on_destinations` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `destination_id` int(10) unsigned NOT NULL,
  `holiday_type_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `UNIQUE` (`destination_id`,`holiday_type_id`),
  KEY `destination_id` (`destination_id`)
) ENGINE=MyISAM AUTO_INCREMENT=671 DEFAULT CHARSET=utf8 ROW_FORMAT=FIXED;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `image_folders`
--

DROP TABLE IF EXISTS `image_folders`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `image_folders` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `parent_id` int(10) unsigned default NULL,
  `lft` int(10) unsigned default NULL,
  `rght` int(10) unsigned default NULL,
  `child_count` int(10) unsigned NOT NULL default '0',
  `direct_child_count` int(10) unsigned NOT NULL default '0',
  `name` varchar(255) NOT NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=342 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `image_versions`
--

DROP TABLE IF EXISTS `image_versions`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `image_versions` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `name` varchar(255) NOT NULL,
  `usage` varchar(255) default NULL,
  `type` varchar(255) default NULL,
  `width` int(10) unsigned NOT NULL default '0',
  `height` int(10) unsigned NOT NULL default '0',
  `force_resize` tinyint(1) unsigned NOT NULL default '0',
  `created` datetime NOT NULL default '0000-00-00 00:00:00',
  `modified` datetime NOT NULL default '0000-00-00 00:00:00',
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `images`
--

DROP TABLE IF EXISTS `images`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `images` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `alt` varchar(255) default NULL,
  `image_folder_id` int(10) unsigned default NULL,
  `extension` varchar(4) NOT NULL default '',
  `versions` text,
  `do_not_resize` tinyint(1) unsigned NOT NULL default '0',
  `created` datetime default NULL,
  `modified` datetime default NULL,
  `published` tinyint(1) NOT NULL default '0',
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=7887 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `images_spotlights`
--

DROP TABLE IF EXISTS `images_spotlights`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `images_spotlights` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `image_id` int(10) unsigned default NULL,
  `spotlight_id` int(10) unsigned default NULL,
  `order` int(10) unsigned default NULL,
  PRIMARY KEY  (`id`),
  KEY `Image` (`image_id`),
  KEY `Spotlight` (`spotlight_id`)
) ENGINE=MyISAM AUTO_INCREMENT=24 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `itineraries`
--

DROP TABLE IF EXISTS `itineraries`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `itineraries` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) default NULL,
  `image_id` int(10) unsigned default NULL,
  `meta_title` varchar(255) default NULL,
  `meta_description` text,
  `meta_keywords` text,
  `summary` text,
  `from_price` float default NULL,
  `map_latitude` float(11,6) NOT NULL default '0.000000',
  `map_longitude` float(11,6) NOT NULL default '0.000000',
  `zoom_level` tinyint(2) unsigned NOT NULL default '0',
  `created` datetime default NULL,
  `modified` datetime default NULL,
  `published` tinyint(1) NOT NULL default '0',
  `google_tracking` text,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=197 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `itinerary_days`
--

DROP TABLE IF EXISTS `itinerary_days`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `itinerary_days` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `itinerary_id` int(10) unsigned NOT NULL,
  `order` tinyint(3) unsigned NOT NULL,
  `day_number` varchar(255) default NULL,
  `name` varchar(255) default NULL,
  `miles` int(10) unsigned default NULL,
  `detail` text,
  `image_id` int(10) unsigned default NULL,
  `latitude` float(11,6) NOT NULL default '0.000000',
  `longitude` float(11,6) NOT NULL default '0.000000',
  `created` datetime default NULL,
  `modified` datetime default NULL,
  `location` varchar(255) default NULL,
  PRIMARY KEY  (`id`),
  KEY `itinerary_id` (`itinerary_id`)
) ENGINE=MyISAM AUTO_INCREMENT=1585 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `landing_pages`
--

DROP TABLE IF EXISTS `landing_pages`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `landing_pages` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `name` varchar(255) NOT NULL default '',
  `slug` varchar(255) default NULL,
  `image_id` int(10) default NULL,
  `meta_title` varchar(255) default NULL,
  `meta_description` text,
  `meta_keywords` text,
  `youtube_playlist_id` varchar(16) default NULL,
  `order` int(10) unsigned default NULL,
  `summary` text,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  `published` tinyint(1) unsigned NOT NULL default '0',
  `google_tracking` text,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `landing_pages_accommodations`
--

DROP TABLE IF EXISTS `landing_pages_accommodations`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `landing_pages_accommodations` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `accommodation_id` int(10) unsigned NOT NULL,
  `landing_page_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned NOT NULL default '0',
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `destination_id` (`landing_page_id`),
  KEY `accommodation_id` (`accommodation_id`)
) ENGINE=MyISAM AUTO_INCREMENT=21 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `landing_pages_activities`
--

DROP TABLE IF EXISTS `landing_pages_activities`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `landing_pages_activities` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `activity_id` int(10) unsigned NOT NULL,
  `landing_page_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `activity_id` (`activity_id`),
  KEY `landing_page_id` (`landing_page_id`)
) ENGINE=MyISAM AUTO_INCREMENT=58 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `landing_pages_images`
--

DROP TABLE IF EXISTS `landing_pages_images`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `landing_pages_images` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `landing_page_id` int(10) unsigned NOT NULL,
  `image_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `landing_page_id` (`landing_page_id`),
  KEY `image_id` (`image_id`)
) ENGINE=MyISAM AUTO_INCREMENT=46 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `landing_pages_itineraries`
--

DROP TABLE IF EXISTS `landing_pages_itineraries`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `landing_pages_itineraries` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `landing_page_id` int(10) unsigned NOT NULL,
  `itinerary_id` int(10) unsigned NOT NULL,
  `order` int(10) unsigned default NULL,
  `featured` tinyint(1) unsigned NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `landing_page_id` (`landing_page_id`),
  KEY `itinerary_id` (`itinerary_id`)
) ENGINE=MyISAM AUTO_INCREMENT=39 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `pages`
--

DROP TABLE IF EXISTS `pages`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `pages` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `parent_id` int(10) unsigned default NULL,
  `lft` int(10) unsigned default NULL,
  `rght` int(10) unsigned default NULL,
  `child_count` int(10) unsigned default NULL,
  `direct_child_count` int(10) unsigned default NULL,
  `css_class` varchar(255) default NULL,
  `internal_ref` varchar(255) default NULL,
  `navigation_label` varchar(255) default NULL,
  `image_id` int(10) unsigned default NULL,
  `slug` varchar(255) default NULL,
  `meta_title` varchar(255) default NULL,
  `meta_description` text,
  `meta_keywords` text,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  `published` tinyint(1) NOT NULL default '0',
  `google_tracking` text,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=60 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `quote_requests`
--

DROP TABLE IF EXISTS `quote_requests`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `quote_requests` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `title` varchar(255) default NULL,
  `firstname` varchar(255) default NULL,
  `lastname` varchar(255) default NULL,
  `telephone` varchar(16) NOT NULL,
  `email` varchar(255) default NULL,
  `email_optin` tinyint(3) unsigned NOT NULL,
  `preferred_day` varchar(255) default NULL,
  `preferred_time` varchar(255) default NULL,
  `house_num_name` varchar(255) default NULL,
  `address1` varchar(255) default NULL,
  `address2` varchar(255) default NULL,
  `address3` varchar(255) default NULL,
  `postcode` varchar(255) default NULL,
  `brochure` int(3) NOT NULL default '0',
  `news_offers_optin` int(3) NOT NULL default '0',
  `num_adults` int(10) NOT NULL default '0',
  `num_children` int(10) unsigned default NULL,
  `child_age` varchar(255) default NULL,
  `preferred_date` varchar(255) default NULL,
  `preferred_duration` varchar(255) default NULL,
  `flexible` int(3) NOT NULL default '0',
  `airport` varchar(255) default NULL,
  `destinations` text,
  `suggest` int(3) NOT NULL default '0',
  `accom_stars` varchar(32) default NULL,
  `car_size` varchar(255) default NULL,
  `other_info` text,
  `how_heard` varchar(255) default NULL,
  `newspaper` varchar(255) default NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=8981 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `spotlights`
--

DROP TABLE IF EXISTS `spotlights`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `spotlights` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `name` varchar(255) default NULL,
  `slug` varchar(255) default NULL,
  `from_price` varchar(255) default NULL,
  `image_id` int(10) unsigned default NULL,
  `summary` text,
  `meta_title` varchar(255) default NULL,
  `meta_description` text,
  `meta_keywords` text,
  `order` int(10) unsigned default NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  `details` text,
  `date_and_price_information` text,
  `dates_and_prices` text,
  `published` tinyint(1) NOT NULL default '0',
  `google_tracking` text,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=250 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `users` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) default NULL,
  `group_id` int(10) unsigned NOT NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=MyISAM AUTO_INCREMENT=27 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `webadmin_menu`
--

DROP TABLE IF EXISTS `navigation_menus`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `navigation_menus` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `parent_id` int(10) unsigned default NULL,
  `lft` int(10) unsigned default NULL,
  `rght` int(10) unsigned default NULL,
  `child_count` int(10) unsigned default NULL,
  `direct_child_count` int(10) unsigned default NULL,
  `name` varchar(255) NOT NULL default '',
  `url` varchar(500) NOT NULL default '',
  `menu_type` varchar(50) NOT NULL default 'main_nav',
  `order` int(10) unsigned default NULL,
  `published` tinyint(1) unsigned NOT NULL default '1',
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`),
  KEY `parent_id` (`parent_id`),
  KEY `menu_type` (`menu_type`),
  KEY `published` (`published`),
  KEY `lft` (`lft`),
  KEY `rght` (`rght`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `webadmin_menu`
--

DROP TABLE IF EXISTS `webadmin_menu`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `webadmin_menu` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `parent_id` int(10) unsigned default NULL,
  `lft` int(10) unsigned default NULL,
  `rght` int(10) unsigned default NULL,
  `child_count` int(10) unsigned default NULL,
  `direct_child_count` int(10) unsigned default NULL,
  `text` varchar(255) default NULL,
  `aco_id` int(10) unsigned default NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=23 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `webadmin_user_profiles`
--

DROP TABLE IF EXISTS `webadmin_user_profiles`;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
CREATE TABLE `webadmin_user_profiles` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `user_id` int(10) unsigned default NULL,
  `name` varchar(255) default NULL,
  `last_login` datetime default NULL,
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=MyISAM AUTO_INCREMENT=27 DEFAULT CHARSET=utf8;
SET character_set_client = @saved_cs_client;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2011-05-25 11:37:04
